{"name": "icbBox", "productName": "证件柜", "version": "0.1.145", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "clean-dist": "node scripts/clean-dist.js", "clean-wsserver": "node scripts/clean-wsserver.js", "electron:build": "npm run clean-dist && npm run clean-wsserver && node src/utils/version-increment.js && vue-cli-service electron:build", "electron:build:deb": "vue-cli-service electron:build --linux --x64", "prepare:linux-icons": "node prepare-linux-icons.js", "electron:serve": "vue-cli-service electron:serve", "postinstall": "electron-builder install-app-deps", "postuninstall": "electron-builder install-app-deps"}, "main": "background.js", "dependencies": {"axios": "^1.7.9", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "electron-store": "^8.1.0", "element-ui": "^2.15.14", "node-wifi": "^2.0.16", "qrcode": "^1.5.4", "tailwindcss": "^3.3.5", "vue": "^2.6.14", "vue-router": "^3.6.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@fortawesome/fontawesome-free": "^6.7.2", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "app-builder-lib": "^26.0.12", "autoprefixer": "^10.4.16", "babel-plugin-component": "^1.1.1", "copy-webpack-plugin": "^13.0.0", "electron": "^22.3.27", "electron-builder": "^23.6.0", "electron-builder-squirrel-windows": "^26.0.12", "electron-devtools-installer": "^3.1.0", "electron-packager": "^17.1.2", "electron-rebuild": "^3.2.9", "eslint-plugin-vue": "^8.0.3", "file-loader": "^6.2.0", "nsis": "^0.0.0", "postcss": "^8.4.31", "sharp": "^0.32.6", "url-loader": "^4.1.1", "vue-cli-plugin-electron-builder": "~2.1.1", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "description": "智能证件柜管理系统 - 支持人脸识别和指纹识别功能的电子证件管理应用", "author": {"name": "证件柜开发团队", "email": "<EMAIL>", "url": "https://example.com"}, "license": "MIT", "homepage": "https://example.com/idcardbox", "engines": {"node": ">=16.0.0"}}